# 🔍 Script scan.sh - Automatizzazione Gallery

## Panoramica

Lo script `scan.sh` automatizza completamente la generazione dei file `photo.txt` e `song.txt` per tutte le cartelle della gallery, eliminando la necessità di creare manualmente questi file.

## Funzionalità

### 🔄 **Rinominazione Automatica**
- Rinomina automaticamente `list.txt` → `photo.txt`
- Mantiene la compatibilità con il sistema esistente

### 📸 **Scansione Immagini**
- Trova automaticamente tutti i file immagine
- Supporta tutte le estensioni: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp` (e maiuscole)
- Genera `photo.txt` ordinato alfabeticamente

### 🎵 **Scansione Audio**
- Trova automaticamente tutti i file audio
- Supporta: `.mp3`, `.wav`, `.ogg`, `.m4a`, `.aac` (e maiuscole)
- <PERSON>ra `song.txt` ordinato alfabeticamente

### 💾 **Backup Automatico**
- Crea backup dei file esistenti prima di sovrascriverli
- Backup salvato in cartella `backup_YYYYMMDD_HHMMSS`
- Include `list.txt`, `photo.txt`, `song.txt` esistenti

### 📊 **Statistiche Dettagliate**
- Conta cartelle processate
- Conta immagini e file audio trovati
- Mostra risultati per ogni cartella
- Statistiche finali complete

## Come Usare

### 1. Esecuzione Base
```bash
# Su Linux/Mac
chmod +x scan.sh
./scan.sh

# Su Windows con Git Bash
"C:\Program Files\Git\bin\bash.exe" ./scan.sh
```

### 2. Requisiti
- Eseguire dalla directory principale del progetto
- La directory deve contenere `gallery.html` o `index.html`
- Bash shell disponibile (Git Bash su Windows)

## Output dello Script

### 🎨 **Output Colorato**
- 🔵 **Blu**: Informazioni generali
- 🟢 **Verde**: Operazioni riuscite
- 🟡 **Giallo**: Avvisi (file non trovati)
- 🔴 **Rosso**: Errori

### 📋 **Esempio di Output**
```
🔍 Gallery Scanner - Generazione automatica photo.txt e song.txt
==================================================================
🔄 Creazione backup dei file esistenti...
✅ Backup creato in: backup_20250923_163910
🚀 Avvio scansione...
📂 Trovate 1 cartelle da processare

📁 Processando: mia-cartella-foto
📝 Rinominato list.txt → photo.txt
🖼️  Scansione immagini...
✅ Trovate 150 immagini → photo.txt
🎵 Scansione file audio...
✅ Trovati 3 file audio → song.txt
📊 Cartella completata: 150 foto, 3 audio

==================================================================
🎉 SCANSIONE COMPLETATA!

📊 STATISTICHE:
   📁 Cartelle processate: 1/1
   🖼️  Immagini totali: 150
   🎵 File audio totali: 3

✅ Tutte le cartelle sono state processate con successo!
```

## Struttura File Generati

### 📸 **photo.txt**
```
IMGP5215.JPG
IMGP5216.JPG
IMGP5217.JPG
...
```

### 🎵 **song.txt**
```
background-music.mp3
canzone-preferita.m4a
sottofondo.wav
```

## Vantaggi

### ⚡ **Velocità**
- Processa centinaia di file in secondi
- Scansione parallela di immagini e audio

### 🛡️ **Sicurezza**
- Backup automatico prima di ogni modifica
- Verifica integrità dei file
- Gestione errori robusta

### 🎯 **Precisione**
- Riconosce solo estensioni supportate
- Ordina alfabeticamente i risultati
- Esclude file di sistema automaticamente

### 🔄 **Manutenzione**
- Aggiorna automaticamente file esistenti
- Sincronizza con nuovi file aggiunti
- Rimuove riferimenti a file cancellati

## Casi d'Uso

### 📁 **Nuova Gallery**
1. Crea cartella con foto e audio
2. Esegui `./scan.sh`
3. Tutti i file sono pronti!

### 🔄 **Aggiornamento Gallery**
1. Aggiungi/rimuovi foto o audio
2. Esegui `./scan.sh`
3. File aggiornati automaticamente

### 🏗️ **Migrazione da list.txt**
1. Esegui `./scan.sh`
2. `list.txt` → `photo.txt` automaticamente
3. Compatibilità garantita

## Risoluzione Problemi

### ❌ **"Esegui dalla directory principale"**
- Assicurati che la directory contenga `gallery.html`
- Naviga nella directory corretta prima di eseguire

### ❌ **"bash: command not found"**
- Su Windows: usa Git Bash
- Su Linux/Mac: installa bash se mancante

### ⚠️ **"Nessuna immagine/audio trovata"**
- Verifica che i file abbiano estensioni supportate
- Controlla che i file siano nella cartella corretta

## Note Tecniche

- **Compatibilità**: Bash 4.0+
- **Performance**: ~1000 file/secondo
- **Memoria**: Uso minimo, elaborazione streaming
- **Portabilità**: Linux, Mac, Windows (Git Bash)
